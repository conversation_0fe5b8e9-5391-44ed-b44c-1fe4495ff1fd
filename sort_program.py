import time
import random
from typing import List, Tuple, Callable

# 定义一个类型别名 Algorithm，表示接受一个整数列表并返回一个整数列表的函数。
# 这使得代码更易读，明确了排序算法的函数签名。
Algorithm = Callable[[List[int]], List[int]]

def bubble_sort(arr: List[int]) -> List[int]:
    """
    冒泡排序 (Bubble Sort)
    这是一种简单直观的排序算法。它重复地遍历要排序的数列，一次比较两个元素，如果它们的顺序错误就把它们交换过来。
    遍历数列的工作是重复地进行直到没有再需要交换，也就是说该数列已经排序完成。

    时间复杂度 (Time Complexity): O(n²)
        - 最坏情况：O(n²) (例如，数组完全逆序)
        - 平均情况：O(n²)
        - 最好情况：O(n) (例如，数组已经有序，通过优化可以提前退出)
        - 解释：当数组中有 n 个元素时，外层循环会执行 n 次。内层循环在每次外层循环中，会比较并可能交换大约 n 次。
          所以总的比较次数大约是 n * n，因此是 O(n²)。
          O(n²) 意味着当数据量 n 增大时，算法的执行时间会以 n 的平方的速度增长，效率较低。

    空间复杂度 (Space Complexity): O(1)
        - 解释：除了输入数组本身，算法只使用了常数个额外的变量（如 i, j, swapped, temp），不随输入数据量 n 的变化而变化，
          所以空间复杂度是 O(1)。O(1) 意味着算法占用的内存空间是固定的，非常节省内存。

    优化：增加一个标志位 `swapped`，如果在一轮（内层循环）中没有发生任何元素交换，
          说明数组已经是有序的，此时可以提前退出排序，提高效率。

    参数:
        arr (List[int]): 待排序的整数列表。

    返回:
        List[int]: 排序后的新列表。
    """
    arr = arr.copy()  # 创建一个数组的副本，以避免修改原始输入数组。
    n = len(arr)      # 获取数组的长度。

    # 外层循环：控制遍历的轮数。每一轮都会将当前未排序部分的最大（或最小）元素“冒泡”到其最终位置。
    # 例如，第 i 轮结束后，最大的 i 个元素已经排好序并位于数组的末尾。
    for i in range(n):
        swapped = False # 标志位，用于检查本轮是否发生了交换。
        # 内层循环：遍历未排序部分，进行相邻元素的比较和交换。
        # `n - i - 1` 是因为每完成一轮外层循环，数组末尾的 i 个元素就已经确定了最终位置，
        # 所以下一轮内层循环只需要遍历到 `n - i - 1` 即可。
        for j in range(0, n - i - 1):
            # 遍历数组从索引 0 到 `n - i - 1`。
            # 如果当前元素 `arr[j]` 大于下一个元素 `arr[j+1]`，则它们顺序错误。
            if arr[j] > arr[j+1]:
                # 交换 `arr[j]` 和 `arr[j+1]` 的位置。
                arr[j], arr[j+1] = arr[j+1], arr[j]
                swapped = True # 发生交换，将标志位设置为 True。
        
        # 如果一轮内层循环结束后，`swapped` 仍然是 False，
        # 说明在这一轮中没有发生任何交换，数组已经完全有序，可以提前退出。
        if not swapped:
            break
    return arr # 返回排序后的数组。

def selection_sort(arr: List[int]) -> List[int]:
    """
    选择排序 (Selection Sort)
    这是一种直观的排序算法。它首先在未排序序列中找到最小（或最大）元素，存放到排序序列的起始位置，
    然后再从剩余未排序元素中继续寻找最小（或最大）元素，然后放到已排序序列的末尾。
    以此类推，直到所有元素均排序完毕。

    时间复杂度 (Time Complexity): O(n²)
        - 最坏情况：O(n²)
        - 平均情况：O(n²)
        - 最好情况：O(n²)
        - 解释：外层循环执行 n 次，内层循环在每次外层循环中，大约执行 n 次比较。
          总的比较次数大约是 n * n，所以是 O(n²)。
          选择排序的比较次数总是固定的，不受输入数据的影响，因此最好、最坏、平均情况都是 O(n²)。

    空间复杂度 (Space Complexity): O(1)
        - 解释：只使用了常数个额外变量，不随输入数据量 n 的变化而变化，所以空间复杂度是 O(1)。

    参数:
        arr (List[int]): 待排序的整数列表。

    返回:
        List[int]: 排序后的新列表。
    """
    arr = arr.copy() # 创建一个数组的副本，以避免修改原始输入数组。
    n = len(arr)     # 获取数组的长度。

    # 外层循环：控制已排序部分的边界。每次循环都会找到一个最小元素并将其放到正确的位置。
    for i in range(n):
        # 假设当前未排序部分的第一个元素 `arr[i]` 是最小的，记录其索引。
        min_idx = i
        # 内层循环：遍历 `i` 之后的未排序部分，寻找真正的最小元素。
        for j in range(i+1, n):
            # 如果找到一个比当前 `min_idx` 处元素更小的元素，则更新 `min_idx`。
            if arr[j] < arr[min_idx]:
                min_idx = j
        
        # 在内层循环结束后，`min_idx` 存储了未排序部分中最小元素的索引。
        # 将找到的最小元素与当前未排序部分的第一个元素 `arr[i]` 交换。
        # 这样，`arr[i]` 就放到了它在最终排序数组中的正确位置。
        arr[i], arr[min_idx] = arr[min_idx], arr[i]
    return arr # 返回排序后的数组。

def insertion_sort(arr: List[int]) -> List[int]:
    """
    插入排序 (Insertion Sort)
    这是一种简单直观的排序算法。它的工作原理是通过构建有序序列，对于未排序数据，
    在已排序序列中从后向前扫描，找到相应位置并插入。

    时间复杂度 (Time Complexity): O(n²)
        - 最坏情况：O(n²) (例如，数组完全逆序)
        - 平均情况：O(n²)
        - 最好情况：O(n) (例如，数组已经有序，只需进行 n-1 次比较)
        - 解释：外层循环执行 n-1 次。内层 `while` 循环在最坏情况下，
          每次都需要将当前元素与已排序部分的所有元素进行比较和移动，大约执行 i 次操作。
          总的操作次数大约是 1 + 2 + ... + (n-1)，所以是 O(n²)。

    空间复杂度 (Space Complexity): O(1)
        - 解释：只使用了常数个额外变量（如 i, j, key），不随输入数据量 n 的变化而变化，所以空间复杂度是 O(1)。

    参数:
        arr (List[int]): 待排序的整数列表。

    返回:
        List[int]: 排序后的新列表。
    """
    arr = arr.copy() # 创建一个数组的副本，以避免修改原始输入数组。
    # 外层循环：从第二个元素开始（索引 1），依次处理每个元素。
    # 假设 `arr[0...i-1]` 是已经排好序的。
    for i in range(1, len(arr)):
        key = arr[i] # `key` 存储当前要插入的元素。
        j = i - 1    # `j` 指向已排序部分的最后一个元素。

        # 内层循环（while 循环）：将 `key` 插入到已排序的 `arr[0...i-1]` 部分的正确位置。
        # 只要 `j` 没有越界（`j >= 0`）并且 `arr[j]` 大于 `key`，
        # 就将 `arr[j]` 向后移动一位，为 `key` 腾出位置。
        while j >= 0 and arr[j] > key:
            arr[j + 1] = arr[j] # 将 `arr[j]` 移动到 `arr[j+1]`。
            j -= 1              # `j` 向前移动一位，继续与前一个元素比较。
        
        # 当 `while` 循环结束时，说明找到了 `key` 的正确插入位置（即 `j+1`），
        # 或者 `key` 是已排序部分中最小的元素（此时 `j` 为 -1）。
        arr[j + 1] = key # 将 `key` 插入到正确的位置。
    return arr # 返回排序后的数组。

def merge_sort(arr: List[int]) -> List[int]:
    """
    归并排序 (Merge Sort)
    这是一种分治算法。它将一个大问题（排序整个数组）分解成小问题（排序子数组），
    然后将小问题的解（已排序的子数组）合并起来，从而解决原始大问题。

    工作原理：
    1. 分解 (Divide)：将待排序的数组从中间一分为二，分成两个子数组。
    2. 解决 (Conquer)：递归地对这两个子数组进行归并排序，直到子数组只包含一个元素（单个元素自然是有序的）。
    3. 合并 (Combine)：将两个已排序的子数组合并成一个更大的已排序数组。

    时间复杂度 (Time Complexity): O(n log n)
        - 最坏情况：O(n log n)
        - 平均情况：O(n log n)
        - 最好情况：O(n log n)
        - 解释：每次递归都将数组分成两半，这产生了 `log n` 层递归。
          在每一层递归中（即在 `merge` 函数中），需要对所有元素进行一次合并操作，
          合并两个长度为 k 的有序数组需要 O(k) 的时间。
          因此，每一层的总操作时间是 O(n)。
          总的时间复杂度是 `log n` 层 * O(n) = O(n log n)。
          O(n log n) 是一种非常高效的排序算法，在大数据量下表现优异。

    空间复杂度 (Space Complexity): O(n)
        - 解释：在合并过程中，需要一个额外的临时数组来存储合并结果，这个临时数组的最大长度与原始数组相同，
          所以空间复杂度是 O(n)。

    参数:
        arr (List[int]): 待排序的整数列表。

    返回:
        List[int]: 排序后的新列表。
    """
    # 递归的终止条件：如果数组长度小于或等于 1，说明它已经是有序的，直接返回。
    if len(arr) <= 1:
        return arr
    
    # 分解 (Divide)：
    # 计算数组的中间索引。
    mid = len(arr) // 2
    # 递归地对左半部分进行归并排序。
    left = merge_sort(arr[:mid])
    # 递归地对右半部分进行归并排序。
    right = merge_sort(arr[mid:])
    
    # 合并 (Combine)：
    # 将两个已排序的子数组 `left` 和 `right` 合并成一个大的有序数组。
    return merge(left, right)

def merge(left: List[int], right: List[int]) -> List[int]:
    """
    归并排序的合并函数。
    将两个已排序的列表 `left` 和 `right` 合并成一个新的已排序列表。

    参数:
        left (List[int]): 第一个已排序的整数列表。
        right (List[int]): 第二个已排序的整数列表。

    返回:
        List[int]: 合并后且排序完成的新列表。
    """
    result = [] # 用于存储合并结果的列表。
    i = j = 0   # `i` 是 `left` 列表的当前索引，`j` 是 `right` 列表的当前索引。
    
    # 比较两个列表中的元素，并将较小的元素添加到 `result` 列表中。
    # 直到其中一个列表的所有元素都被处理完毕。
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i]) # 将 `left` 中的当前元素添加到结果。
            i += 1                 # `left` 的索引向前移动。
        else:
            result.append(right[j]) # 将 `right` 中的当前元素添加到结果。
            j += 1                  # `right` 的索引向前移动。
    
    # 将 `left` 列表中剩余的元素（如果有的话）全部添加到 `result`。
    # 因为 `left` 和 `right` 本身已经是有序的，所以直接添加即可。
    result.extend(left[i:])
    # 将 `right` 列表中剩余的元素（如果有的话）全部添加到 `result`。
    result.extend(right[j:])
    
    return result # 返回合并后的有序列表。

def quick_sort(arr: List[int]) -> List[int]:
    """
    快速排序 (Quick Sort)
    这是一种高效的分治排序算法。它通过一趟排序将待排序的数据分割成独立的两部分，
    其中一部分的所有数据都比另外一部分的所有数据都要小，然后再按此方法对这两部分数据分别进行快速排序。

    工作原理：
    1. 选择基准 (Pivot Selection)：从数组中选择一个元素作为“基准”（pivot）。
    2. 分区 (Partitioning)：重新排列数组，使得所有比基准值小的元素都移动到基准的左边，
       所有比基准值大的元素都移动到基准的右边。相同值的元素可以放到任何一边。
       在这个分区结束之后，该基准就处于数组的最终正确位置。
    3. 递归 (Recursion)：递归地对基准左右两边的子数组重复上述过程，直到子数组只有一个元素或为空。

    时间复杂度 (Time Complexity):
        - 平均情况：O(n log n) (性能非常好，因为常数因子较小)
        - 最坏情况：O(n²) (例如，每次都选择最大或最小元素作为基准，导致每次只划分出一个元素)
        - 最好情况：O(n log n)

    空间复杂度 (Space Complexity): O(log n)
        - 解释：快速排序是原地排序（in-place sorting），它不需要额外的存储空间，
          但递归调用栈会占用 O(log n) 的空间（在最坏情况下可能达到 O(n)）。

    注意：这个函数是一个包装函数，它会复制一份数组，然后调用一个原地排序的辅助函数 `_quick_sort_inplace`。
          这样做是为了保持函数接口的一致性（即不修改原始输入数组，而是返回一个新数组）。

    参数:
        arr (List[int]): 待排序的整数列表。

    返回:
        List[int]: 排序后的新列表。
    """
    # 递归的终止条件：如果数组长度小于或等于 1，说明它已经是有序的，直接返回。
    if len(arr) <= 1:
        return arr
    arr_copy = arr.copy() # 创建一个数组的副本，以避免修改原始输入数组。
    # 调用原地快速排序的辅助函数，对数组副本进行排序。
    # `0` 是起始索引，`len(arr_copy) - 1` 是结束索引。
    _quick_sort_inplace(arr_copy, 0, len(arr_copy) - 1)
    return arr_copy # 返回排序后的数组。

def _quick_sort_inplace(arr: List[int], low: int, high: int):
    """
    快速排序的原地实现辅助函数。
    这个函数直接修改传入的数组 `arr`。

    参数:
        arr (List[int]): 待排序的整数列表（会被修改）。
        low (int): 当前子数组的起始索引。
        high (int): 当前子数组的结束索引。
    """
    # 递归的终止条件：如果 `low` 大于或等于 `high`，说明当前子数组只有一个元素或为空，无需排序。
    if low < high:
        # 调用 `_partition` 函数，对当前子数组进行分区操作。
        # `_partition` 会返回基准元素在排序后的最终位置（索引 `pi`）。
        pi = _partition(arr, low, high)
        
        # 递归地对基准左边的子数组进行快速排序。
        _quick_sort_inplace(arr, low, pi - 1)
        # 递归地对基准右边的子数组进行快速排序。
        _quick_sort_inplace(arr, pi + 1, high)

def _partition(arr: List[int], low: int, high: int) -> int:
    """
    Lomuto 分区方案 (Lomuto Partition Scheme)。
    这是快速排序中常用的分区方法之一。
    它选择数组的最后一个元素作为基准 (pivot)，然后将数组分为两部分：
    左边是小于或等于基准的元素，右边是大于基准的元素。

    参数:
        arr (List[int]): 待分区的整数列表（会被修改）。
        low (int): 当前子数组的起始索引。
        high (int): 当前子数组的结束索引（基准元素所在的索引）。

    返回:
        int: 基准元素最终所在的索引。
    """
    # 选择最后一个元素 `arr[high]` 作为基准 (pivot)。
    pivot = arr[high]
    # `i` 是一个指针，它指向“小于或等于基准”区域的最后一个元素的位置。
    # 初始化为 `low - 1`，表示一开始这个区域是空的。
    i = low - 1

    # 遍历当前子数组中除了基准元素之外的所有元素（从 `low` 到 `high-1`）。
    for j in range(low, high):
        # 如果当前元素 `arr[j]` 小于或等于基准 `pivot`。
        if arr[j] <= pivot:
            i += 1 # `i` 向右移动一位，为新找到的小于或等于基准的元素腾出位置。
            # 交换 `arr[i]` 和 `arr[j]`。
            # 这确保了 `arr[i]` 及其左边的所有元素都小于或等于基准。
            arr[i], arr[j] = arr[j], arr[i]

    # 循环结束后，`i` 指向了所有小于或等于基准的元素区域的最后一个位置。
    # 将基准元素（最初在 `arr[high]`）放到它在排序后数组中的最终正确位置。
    # 它的位置应该在所有小于或等于它的元素之后，所有大于它的元素之前。
    # 所以将 `arr[i+1]` （即第一个大于基准的元素，或者如果所有元素都小于等于基准，则是 `arr[high]` 的位置）
    # 与 `arr[high]` （基准）交换。
    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    # 返回基准元素最终所在的索引。
    return i + 1


def heap_sort(arr: List[int]) -> List[int]:
    """
    堆排序 (Heap Sort)
    这是一种基于比较的排序算法，利用了堆（一种特殊的完全二叉树）的数据结构。
    堆排序首先将待排序的数组构建成一个最大堆（或最小堆），然后重复地将堆顶元素（最大或最小）
    与堆的最后一个元素交换，并缩小堆的范围，再对剩余元素进行堆化，直到堆为空。

    工作原理：
    1. 构建最大堆 (Build Max-Heap)：将输入的无序数组构造成一个最大堆。
       最大堆的性质是：每个父节点的值都大于或等于其子节点的值。
    2. 堆排序 (Heap Sort)：
       - 将堆顶元素（最大值）与堆的最后一个元素交换。
       - 此时，最大的元素已经放置在数组的末尾。
       - 缩小堆的范围（不再包括已排序的最后一个元素）。
       - 对新的堆顶元素进行堆化（heapify），使其满足最大堆的性质。
       - 重复上述步骤，直到堆中只剩下一个元素。

    时间复杂度 (Time Complexity): O(n log n)
        - 最坏情况：O(n log n)
        - 平均情况：O(n log n)
        - 最好情况：O(n log n)
        - 解释：构建堆需要 O(n) 的时间。在排序阶段，需要执行 n-1 次提取最大元素的操作，
          每次提取后都需要进行一次堆化操作，而堆化操作的时间复杂度是 O(log n)。
          因此，总的时间复杂度是 O(n + n log n) = O(n log n)。

    空间复杂度 (Space Complexity): O(1)
        - 解释：堆排序是原地排序，不需要额外的存储空间，所有的操作都在原始数组上进行。
          除了递归调用栈（在 `heapify` 中，但通常可以写成迭代形式）和少量变量，空间使用是常数级别的。

    参数:
        arr (List[int]): 待排序的整数列表。

    返回:
        List[int]: 排序后的新列表。
    """
    arr = arr.copy() # 创建一个数组的副本，以避免修改原始输入数组。
    
    def heapify(arr, n, i):
        """
        堆化函数 (Heapify)
        用于维护堆的性质。它假设以 `i` 为根的子树的左右子树已经是最大堆，
        但 `i` 节点可能不满足最大堆性质，所以需要调整。

        参数:
            arr (List[int]): 列表，表示堆。
            n (int): 堆的大小（当前需要堆化的数组部分的长度）。
            i (int): 需要进行堆化的子树的根节点索引。
        """
        largest = i  # 假设当前根节点 `i` 是最大元素。
        left = 2 * i + 1  # 计算左子节点的索引。
        right = 2 * i + 2 # 计算右子节点的索引。
        
        # 如果左子节点存在（`left < n`）并且左子节点的值大于当前最大值 `arr[largest]`。
        if left < n and arr[left] > arr[largest]:
            largest = left # 更新 `largest` 为左子节点的索引。
        
        # 如果右子节点存在（`right < n`）并且右子节点的值大于当前最大值 `arr[largest]`。
        if right < n and arr[right] > arr[largest]:
            largest = right # 更新 `largest` 为右子节点的索引。
        
        # 如果 `largest` 不再是原来的根节点 `i`，说明子节点比根节点大，需要进行交换。
        if largest != i:
            arr[i], arr[largest] = arr[largest], arr[i] # 交换根节点和最大子节点。
            # 递归地对被交换的子树进行堆化，因为交换可能破坏了其子堆的性质。
            heapify(arr, n, largest)
    
    n = len(arr) # 获取数组的长度。
    
    # 构建最大堆 (Build Max-Heap)：
    # 从最后一个非叶子节点开始（索引 `n // 2 - 1`）向前遍历，对每个节点执行 `heapify`。
    # 这样可以确保从下往上，逐步将整个数组构建成一个最大堆。
    for i in range(n // 2 - 1, -1, -1):
        heapify(arr, n, i)
    
    # 提取元素并排序 (Extract elements from heap one by one)：
    # 从数组的最后一个元素开始，向前遍历。
    for i in range(n-1, 0, -1):
        # 将当前堆顶元素（最大值 `arr[0]`）与当前堆的最后一个元素 `arr[i]` 交换。
        # 这样，最大值就被放置在了数组的末尾（已排序部分）。
        arr[i], arr[0] = arr[0], arr[i]
        # 对剩余的 `i` 个元素（不包括刚刚放到末尾的元素）组成的堆进行堆化。
        # 此时堆的大小缩小为 `i`。
        heapify(arr, i, 0)
    
    return arr # 返回排序后的数组。

def counting_sort(arr: List[int]) -> List[int]:
    """
    计数排序 (Counting Sort)
    这是一种非比较排序算法，适用于整数排序。
    它的核心思想是统计每个整数在数组中出现的次数，然后根据这些统计信息来构建排序后的数组。

    适用条件：
    - 适用于整数类型的数据。
    - 数据的范围（最大值和最小值之间的差值 `k`）不宜过大，否则会占用大量内存。
      （因为需要创建一个大小为 `k` 的计数数组）

    时间复杂度 (Time Complexity): O(n + k)
        - 解释：`n` 是输入数组的长度，`k` 是输入数据范围（最大值 - 最小值 + 1）。
          - 寻找最大值和最小值需要 O(n) 时间。
          - 创建计数数组并初始化需要 O(k) 时间。
          - 统计每个元素的出现次数需要 O(n) 时间。
          - 构建结果数组需要 O(n + k) 时间。
          - 所以总的时间复杂度是 O(n + k)。
          当 `k` 相对 `n` 不大时，计数排序可以比 O(n log n) 的比较排序更快。

    空间复杂度 (Space Complexity): O(k)
        - 解释：需要一个额外的计数数组 `count`，其大小取决于输入数据的范围 `k`。
          所以空间复杂度是 O(k)。

    参数:
        arr (List[int]): 待排序的整数列表。

    返回:
        List[int]: 排序后的新列表。
    """
    # 如果输入数组为空，直接返回空数组。
    if not arr:
        return arr
    
    # 1. 找到输入数组中的最大值和最小值，以确定数据的范围。
    max_val = max(arr)
    min_val = min(arr)
    # 计算数据范围的大小。例如，如果 min_val=1, max_val=5，则范围大小为 5-1+1 = 5。
    range_val = max_val - min_val + 1
    
    # 2. 创建一个 `count` 数组（或称为计数器数组），用于存储每个数字出现的次数。
    # 数组的长度为 `range_val`，所有元素初始化为 0。
    # `count[i]` 将存储数字 `min_val + i` 在原始数组中出现的次数。
    count = [0] * range_val
    
    # 3. 遍历原始数组 `arr`，统计每个数字出现的频率。
    for num in arr:
        # 将数字 `num` 映射到 `count` 数组的相应索引。
        # 例如，如果 min_val 是 10，num 是 12，那么 12-10=2，对应 count[2]。
        count[num - min_val] += 1
    
    # 4. 根据 `count` 数组构建排序后的结果数组。
    result = [] # 用于存储最终排序结果的列表。
    # 遍历 `count` 数组的每个索引 `i`。
    for i in range(range_val):
        # `count[i]` 表示数字 `min_val + i` 出现了 `count[i]` 次。
        # 将数字 `min_val + i` 重复 `count[i]` 次，并添加到 `result` 列表中。
        result.extend([i + min_val] * count[i])
    
    return result # 返回排序后的数组。

# 将所有排序算法函数及其名称组织成一个列表，方便统一管理和测试。
# 列表中的每个元素是一个元组 (算法名称, 算法函数)。
ALL_ALGORITHMS: List[Tuple[str, Algorithm]] = [
    ("冒泡排序", bubble_sort),
    ("选择排序", selection_sort),
    ("插入排序", insertion_sort),
    ("归并排序", merge_sort),
    ("快速排序", quick_sort),
    ("堆排序", heap_sort),
    ("计数排序", counting_sort),
]

def test_sorting_algorithms():
    """
    测试所有排序算法的基本功能和正确性。
    对预定义的一系列小规模测试用例运行每种排序算法，并打印结果和耗时。
    """
    print("--- 基本功能测试 ---")
    # 定义一系列测试用例，包括：
    # - 随机顺序的数组
    # - 少量元素的数组
    # - 只有一个元素的数组
    # - 空数组
    # - 包含重复元素的数组
    # - 完全逆序的数组
    # - 已经排好序的数组
    test_cases = [
        [64, 34, 25, 12, 22, 11, 90],
        [5, 1, 4, 2, 8],
        [1],
        [],
        [3, 3, 3, 3],
        list(range(100, 0, -1)),  # 逆序数组，从100到1
        list(range(100)),        # 已排序数组，从0到99
    ]

    # 遍历每个测试用例。
    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_case}") # 打印当前测试用例的原始数组。
        
        # 遍历 `ALL_ALGORITHMS` 中定义的每种排序算法。
        for name, algorithm in ALL_ALGORITHMS:
            try:
                start_time = time.time() # 记录算法开始执行的时间。
                result = algorithm(test_case) # 调用排序算法对测试用例进行排序。
                end_time = time.time()   # 记录算法结束执行的时间。
                
                # 打印算法名称、排序结果以及执行所花费的时间（精确到微秒）。
                print(f"{name}: {result} (耗时: {end_time - start_time:.6f}s)")
            except Exception as e:
                # 如果算法执行过程中发生错误，捕获异常并打印错误信息。
                print(f"{name}: 错误 - {e}")

def performance_test():
    """
    性能测试。
    比较不同排序算法在大数据集上的执行效率。
    主要关注时间复杂度为 O(n log n) 的高效算法，并与 Python 内置的 `sorted()` 函数进行比较。
    """
    print("\n--- 性能测试 ---")
    # 定义不同的数据规模（数组长度）。
    sizes = [100, 1000, 5000, 10000] # 添加一个更大的规模，以便更明显地看出性能差异。
    
    # 只选择通常效率较高（时间复杂度为 O(n log n) 或更优）的算法进行性能测试。
    # O(n²) 的算法在数据量大时会非常慢。
    efficient_algorithms: List[Tuple[str, Algorithm]] = [
        ("归并排序", merge_sort),
        ("快速排序", quick_sort),
        ("堆排序", heap_sort),
        ("计数排序", counting_sort), # 计数排序在特定条件下非常高效
        ("Python内置sort", sorted)   # 加入 Python 内置的 sorted() 函数作为基准，
                                     # 它通常是 Timsort，一种混合排序算法，效率很高。
    ]
    
    print("\n性能测试:")
    print("=" * 60) # 打印分隔线，增强可读性。
    
    # 遍历不同的数据规模。
    for size in sizes:
        print(f"\n数据规模: {size} 个元素") # 打印当前测试的数据规模。
        # 生成指定大小的随机整数列表作为测试数据。
        # 随机范围为 1 到 1000。
        test_data = [random.randint(1, 1000) for _ in range(size)]
        
        # 遍历高效算法列表。
        for name, algorithm in efficient_algorithms:
            try:
                start_time = time.time() # 记录算法开始执行的时间。
                # 对测试数据进行排序。
                # 注意：这里调用 algorithm(test_data) 会创建一个副本，因为所有排序函数都返回新列表。
                # 对于 sorted() 函数，它本身就返回一个新列表。
                algorithm(test_data)
                end_time = time.time()   # 记录算法结束执行的时间。
                
                # 打印算法名称和执行所花费的时间。
                print(f"{name}: {end_time - start_time:.6f}s")
            except Exception as e:
                # 如果算法执行过程中发生错误，捕获异常并打印错误信息。
                print(f"{name}: 错误 - {e}")

if __name__ == "__main__":
    # 这是一个 Python 的标准用法，表示只有当这个脚本作为主程序直接运行时，
    # 才会执行 `if __name__ == "__main__"` 块中的代码。
    # 如果这个脚本被其他文件导入，这部分代码就不会执行。
    
    print("排序算法演示程序")
    print("=" * 50) # 打印分隔线。
    
    # 执行基本功能测试。
    test_sorting_algorithms()
    
    # 执行性能测试。
    performance_test()
    
    # 交互式测试：允许用户输入数字进行排序。
    print("\n--- 交互式测试 ---")
    while True: # 进入一个无限循环，直到用户输入 'quit' 退出。
        try:
            # 提示用户输入数字，并用空格分隔。
            user_input = input("\n请输入要排序的数字（用空格分隔），或输入 'quit' 退出: ")
            if user_input.lower() == 'quit': # 如果用户输入 'quit' (不区分大小写)，则退出循环。
                break
            
            # 将用户输入的字符串通过空格分割，并将每个部分转换为整数，存储到 `numbers` 列表中。
            numbers = list(map(int, user_input.split()))
            
            print(f"原始数组: {numbers}") # 打印用户输入的原始数组。
            # 使用快速排序对数组进行排序并打印结果。
            print(f"快速排序结果: {quick_sort(numbers)}")
            # 使用归并排序对数组进行排序并打印结果。
            print(f"归并排序结果: {merge_sort(numbers)}")
            
        except ValueError:
            # 如果用户输入了非数字字符，`map(int, ...)` 会抛出 ValueError。
            # 捕获该异常并提示用户输入格式错误。
            print("输入格式错误，请输入数字！")
        except KeyboardInterrupt:
            # 如果用户在程序运行时按下 Ctrl+C (KeyboardInterrupt)，则捕获该异常并退出程序。
            print("\n程序退出")
            break
