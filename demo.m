%生成一个随机数列，1-100,10个
num = randi([1, 100], 1, 10);

bubble = bubblesort(num);
disp("排序前的随机数列:");
disp(num);
disp("排序后的随机数列:");
disp(bubble);


function sorted_array = bubblesort(array)
    % Bubblesort algorithm to sort an array in ascending order
    n = length(array);
    for i = 1:n-1
        for j = 1:n-i
            if array(j) > array(j+1)
                % Swap the elements
                temp = array(j);
                array(j) = array(j+1);
                array(j+1) = temp;
            end
        end
    end
    sorted_array = array;
end

