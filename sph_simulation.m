% SPH (Smoothed Particle Hydrodynamics) 简化MATLAB实现示例
% 这是一个非常基础的SPH模拟示例，用于演示核心概念。
% 它省略了许多高级特性和优化，例如邻居搜索优化（网格划分）、
% 更复杂的边界条件、更精确的积分器、更稳定的状态方程和核函数。

clear; clc; close all;

%% 1. 初始化模拟参数
N = 100; % 粒子数量
h = 0.1; % 光滑长度 (Smoothing Length)
dt = 0.001; % 时间步长 (Time Step)
total_time = 2.0; % 总模拟时间
rest_density = 1000; % 静止密度 (kg/m^3)
k = 1.0; % 气体常数 (或状态方程参数)
mu = 0.1; % 粘度系数 (Viscosity Coefficient)
g = [0, -9.81]; % 重力向量 (m/s^2)

% 粒子属性初始化
% 初始位置：随机分布在一个小区域内
positions = rand(N, 2) * 0.5; % 2D 模拟
velocities = zeros(N, 2);
masses = ones(N, 1) * (rest_density * (h^2)); % 假设粒子质量相等，并基于初始密度和光滑长度估算

densities = zeros(N, 1);
pressures = zeros(N, 1);
forces = zeros(N, 2);
accelerations = zeros(N, 2);

% 定义核函数及其梯度 (简单的三次样条核函数，2D)
% W(r, h) = alpha * (1 - r/h)^3 for r <= h
% dW(r, h)/dr = alpha * 3 * (1 - r/h)^2 * (-1/h)
% alpha = 10 / (7 * pi * h^2) for 2D
alpha_W = 10 / (7 * pi * h^2);
alpha_dW = -30 / (7 * pi * h^3); % for gradient magnitude

W = @(r, h_val) (r <= h_val) .* alpha_W .* (1 - r/h_val).^3;
dW_dr = @(r, h_val) (r <= h_val) .* alpha_dW .* (1 - r/h_val).^2;

% 边界定义 (一个简单的矩形区域)
x_min = 0; x_max = 1;
y_min = 0; y_max = 1;

% 绘制初始粒子分布
figure;
plot(positions(:,1), positions(:,2), 'o', 'MarkerSize', 5, 'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'r');
title('Initial Particle Distribution');
xlabel('X Position');
ylabel('Y Position');
xlim([x_min, x_max]);
ylim([y_min, y_max]);
grid on;
hold off; % 确保这个图不会被后续动画更新

%% 模拟主循环
num_steps = total_time / dt;

figure;
hold on;
title('SPH Fluid Simulation');
xlabel('X Position');
ylabel('Y Position');
xlim([x_min, x_max]);
ylim([y_min, y_max]);
grid on;

h_plot = plot(positions(:,1), positions(:,2), 'o', 'MarkerSize', 5, 'MarkerFaceColor', 'b', 'MarkerEdgeColor', 'b');

for step = 1:num_steps
    
    % 2. 计算每个粒子的密度和压力
    for i = 1:N
        densities(i) = 0;
        for j = 1:N
            r_ij = positions(i,:) - positions(j,:);
            distance_ij = norm(r_ij);
            densities(i) = densities(i) + masses(j) * W(distance_ij, h);
        end
        % 简单的状态方程 (类似理想气体)
        pressures(i) = k * (densities(i) - rest_density);
        if pressures(i) < 0
            pressures(i) = 0; % 防止负压
        end
    end

    % 3. 计算每个粒子的受力
    forces(:) = 0; % 重置力
    for i = 1:N
        pressure_force = [0, 0];
        viscosity_force = [0, 0];

        for j = 1:N
            if i == j, continue; end % 跳过自身

            r_ij = positions(i,:) - positions(j,:);
            distance_ij = norm(r_ij);

            if distance_ij < h && distance_ij > 1e-9 % 避免除以零
                % 压力力
                dW_val = dW_dr(distance_ij, h);
                pressure_term = (pressures(i) / (densities(i)^2) + pressures(j) / (densities(j)^2));
                pressure_force = pressure_force - masses(j) * pressure_term * (r_ij / distance_ij) * dW_val;

                % 粘性力
                v_ij = velocities(i,:) - velocities(j,:);
                viscosity_term = (v_ij * r_ij') / (distance_ij^2 + 0.01 * h^2); % 点积
                viscosity_force = viscosity_force + masses(j) * (2 * mu * viscosity_term * (r_ij / distance_ij)) * dW_val;
            end
        end
        forces(i,:) = pressure_force + viscosity_force + masses(i) * g;
    end

    % 4. 更新粒子速度和位置 (欧拉积分)
    accelerations = forces ./ masses;
    velocities = velocities + accelerations * dt;
    positions = positions + velocities * dt;

    % 5. 处理边界条件 (简单反弹)
    for i = 1:N
        if positions(i,1) < x_min
            positions(i,1) = x_min;
            velocities(i,1) = -velocities(i,1) * 0.5; % 简单反弹，能量损失
        elseif positions(i,1) > x_max
            positions(i,1) = x_max;
            velocities(i,1) = -velocities(i,1) * 0.5;
        end
        if positions(i,2) < y_min
            positions(i,2) = y_min;
            velocities(i,2) = -velocities(i,2) * 0.5;
        elseif positions(i,2) > y_max
            positions(i,2) = y_max;
            velocities(i,2) = -velocities(i,2) * 0.5;
        end
    end

    % 绘制结果
    set(h_plot, 'XData', positions(:,1), 'YData', positions(:,2));
    drawnow limitrate;
end

disp('SPH Simulation Finished.');
