% plot_periodic_hill.m

% MATLAB 脚本，用于重现 Incompact3d 中周期山轮廓的几何形状。
% 该代码基于 Fortran 文件 Case-Periodic-hill.f90 中的 geomcomplex_hill 子程序。

clear; % 清除工作区变量
clc;   % 清除命令行窗口

% --- 参数定义 ---
% 以下参数需要根据您的 Incompact3d 模拟设置进行调整。
% 这里给出了一些常见的假设值。
xlx = 9.0;    % 周期山的总长度（例如，Incompact3d 中的 x 域长度）
nx = 512;     % x 方向的网格点数。可以根据需要调整分辨率。
dx = xlx / nx; % 网格步长

% --- Fortran 代码中使用的常数映射 (来自 param 模块的推断值) ---
zero = 0.0;
one = 1.0;
two = 2.0;
nine = 9.0;
fourteen = 14.0;
twenty = 20.0;
thirty = 30.0;
forty = 40.0;
fiftyfour = 54.0;
twentyeight = 28.0; % Fortran 代码中直接使用的常数

% --- 初始化数组 ---
x_original = zeros(1, nx); % 存储原始 x 坐标，用于绘图
dune_contour = zeros(1, nx); % 存储计算出的山轮廓高度

% --- 计算山轮廓 ---
for i = 1:nx
    % 计算原始 xm 值 (对应 Fortran 中的 real(i-1,mytype)*dx)
    xm_current = (i - 1) * dx;
    x_original(i) = xm_current; % 保存原始 x 坐标用于绘图

    % Fortran 逻辑中的 xm 变换
    % 这部分逻辑将原始 x 坐标映射到一个用于 y_bump 计算的特定范围
    if xm_current > xlx / two
        xm_transformed = (xlx - xm_current) * twentyeight;
    else
        xm_transformed = xm_current * twentyeight;
    end

    y_bump = 0.0; % 每次循环重置 y_bump

    % 根据 xm_transformed 的值应用分段函数计算 y_bump
    if (xm_transformed >= zero) && (xm_transformed <= nine)
        y_bump = max(zero, 7.202303030303E-02 + 2.385496969697E-02 * xm_transformed ...
                     - 2.079999999999E-03 * xm_transformed^2 + 7.999999999999E-05 * xm_transformed^3);
    elseif (xm_transformed >= nine) && (xm_transformed <= fiftyfour)
        % Fortran 代码中，从 9 到 54 的所有区间都使用了相同的公式
        y_bump = max(zero, 5.639011190988E+01 - 2.010520359035E+00 * xm_transformed ...
                     + 1.644919857549E-02 * xm_transformed^2 + 2.674976141766E-05 * xm_transformed^3);
    end
    
    % 将 y_bump 归一化（除以 twentyeight），得到最终的山轮廓高度
    dune_contour(i) = y_bump / twentyeight;
end

% --- 绘图 ---
figure; % 创建新的图形窗口
plot(x_original, dune_contour, 'b-', 'LineWidth', 1.5);
title('Periodic Hill Contour (Recreated from Incompact3d Fortran Code)');
xlabel('x');
ylabel('Hill Height');
grid on;
axis tight; % 调整坐标轴范围以适应数据

disp('周期山轮廓已计算并绘制完成。');
