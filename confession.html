<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心形表白页面</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=ZCOOL+KuaiLe&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'ZCOOL KuaiLe', cursive;
            overflow: hidden;
        }
        
        .container {
            text-align: center;
            position: relative;
            z-index: 10;
        }
        
        .heart-container {
            margin: 30px auto 50px;
            cursor: pointer;
            position: relative; /* For particle effects */
            width: 250px;
            height: 250px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .heart-svg {
            width: 100%;
            height: 100%;
            color: #ff6b6b;
            animation: heartbeat 1.5s ease-in-out infinite;
        }
        
        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .confession-text {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .main-text {
            font-size: 24px;
            line-height: 1.8;
            color: #ffffff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
            opacity: 0;
            animation: fadeInUp 2s ease-out 1s forwards;
        }
        
        .highlight {
            color: #ff6b6b;
            font-size: 36px;
            font-weight: bold;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5), 0 0 20px #ff6b6b; }
            to { text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5), 0 0 30px #ff6b6b, 0 0 40px #ff6b6b; }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .floating-hearts {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            top: 0;
            left: 0;
            z-index: 1;
        }
        
        .floating-heart {
            position: absolute;
            font-size: 20px;
            color: rgba(255, 107, 107, 0.7);
            animation: float 6s infinite linear;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
        
        .sparkles {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 5;
        }
        
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffffff;
            border-radius: 50%;
            animation: sparkle 2s infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            10% { transform: translate(-2px, -2px) rotate(-1deg); }
            20% { transform: translate(2px, -2px) rotate(1deg); }
            30% { transform: translate(-2px, 2px) rotate(0deg); }
            40% { transform: translate(2px, 2px) rotate(1deg); }
            50% { transform: translate(-2px, -2px) rotate(-1deg); }
            60% { transform: translate(2px, -2px) rotate(0deg); }
            70% { transform: translate(-2px, 2px) rotate(-1deg); }
            80% { transform: translate(2px, 2px) rotate(1deg); }
            90% { transform: translate(-2px, -2px) rotate(0deg); }
        }
        
        @keyframes screenShake {
            0%, 100% { transform: translate(0, 0); }
            10% { transform: translate(-1px, -1px); }
            20% { transform: translate(1px, -1px); }
            30% { transform: translate(-1px, 1px); }
            40% { transform: translate(1px, 1px); }
            50% { transform: translate(-1px, -1px); }
            60% { transform: translate(1px, -1px); }
            70% { transform: translate(-1px, 1px); }
            80% { transform: translate(1px, 1px); }
            90% { transform: translate(-1px, -1px); }
        }
        
        @keyframes heartRain {
            0% {
                transform: translateY(-50px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        .subtitle {
            font-size: 18px;
            color: #ffd700;
            margin-top: 20px;
            opacity: 0;
            animation: fadeInUp 2s ease-out 3s forwards;
        }
        
        @media (max-width: 768px) {
            .heart {
                width: 150px;
                height: 150px;
            }
            
            .heart::before,
            .heart::after {
                width: 75px;
                height: 120px;
                border-radius: 37.5px 37.5px 0 0;
            }
            
            .heart::after {
                left: 37.5px;
            }
            
            .main-text {
                font-size: 18px;
            }
            
            .highlight {
                font-size: 28px;
            }
            
            .confession-text {
                padding: 20px;
                margin: 0 10px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-hearts"></div>
    <div class="sparkles"></div>
    
    <div class="container">
        <div class="heart-container">
            <svg class="heart-svg" viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z" />
            </svg>
        </div>
        
        <div class="confession-text">
            <div class="main-text">
                曾经有一份真诚的爱情放在我面前，<br>
                我没有珍惜，等我失去的时候我才后悔莫及，<br>
                人世间最痛苦的事莫过于此。<br><br>
                如果上天能够给我一个再来一次的机会，<br>
                我会对那个女孩子说三个字：<br>
                <strong style="color: #ff6b6b; font-size: 32px;">我爱你</strong><br><br>
                如果非要在这份爱上加上一个期限，<br>
                我希望是……<br>
                <span class="highlight">一万年！</span>
            </div>
            
            <div class="subtitle">
                —— 致我最爱的人 ❤️
            </div>
        </div>
    </div>
    
    <script>
        // 创建漂浮的心形
        function createFloatingHearts() {
            const heartsContainer = document.querySelector('.floating-hearts');
            
            setInterval(() => {
                const heart = document.createElement('div');
                heart.className = 'floating-heart';
                heart.innerHTML = '❤️';
                heart.style.left = Math.random() * 100 + '%';
                heart.style.animationDelay = Math.random() * 2 + 's';
                heart.style.animationDuration = (Math.random() * 3 + 6) + 's';
                heartsContainer.appendChild(heart);
                
                setTimeout(() => {
                    heart.remove();
                }, 6000);
            }, 800);
        }
        
        // 创建闪烁的星星
        function createSparkles() {
            const sparklesContainer = document.querySelector('.sparkles');
            
            setInterval(() => {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                sparkle.style.left = Math.random() * 100 + '%';
                sparkle.style.top = Math.random() * 100 + '%';
                sparkle.style.animationDelay = Math.random() * 1 + 's';
                sparklesContainer.appendChild(sparkle);
                
                setTimeout(() => {
                    sparkle.remove();
                }, 2000);
            }, 300);
        }
        
        // 点击心形的特效
        document.querySelector('.heart-container').addEventListener('click', function() {
            // 心形震动效果
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'heartbeat 0.3s ease-in-out 5, shake 0.5s ease-in-out';
            }, 10);
            
            // 屏幕震动效果
            document.body.style.animation = 'screenShake 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.animation = '';
            }, 500);
            
            // 创建更强烈的爆炸效果
            const effects = ['💕', '❤️', '💖', '💗', '💝', '💘', '✨', '🌟', '💫'];
            
            // 第一圈爆炸
            for (let i = 0; i < 16; i++) {
                const effect = document.createElement('div');
                effect.innerHTML = effects[Math.floor(Math.random() * effects.length)];
                effect.style.position = 'absolute';
                effect.style.left = '50%';
                effect.style.top = '50%';
                effect.style.transform = 'translate(-50%, -50%)';
                effect.style.fontSize = (Math.random() * 20 + 20) + 'px';
                effect.style.pointerEvents = 'none';
                effect.style.animation = `explode 1.5s ease-out forwards`;
                effect.style.setProperty('--angle', (i * 22.5) + 'deg');
                effect.style.setProperty('--distance', (Math.random() * 100 + 100) + 'px');
                effect.style.zIndex = '1000';
                this.appendChild(effect);
                
                setTimeout(() => {
                    effect.remove();
                }, 1500);
            }
            
            // 第二圈延迟爆炸
            setTimeout(() => {
                for (let i = 0; i < 12; i++) {
                    const effect = document.createElement('div');
                    effect.innerHTML = effects[Math.floor(Math.random() * effects.length)];
                    effect.style.position = 'absolute';
                    effect.style.left = '50%';
                    effect.style.top = '50%';
                    effect.style.transform = 'translate(-50%, -50%)';
                    effect.style.fontSize = (Math.random() * 15 + 15) + 'px';
                    effect.style.pointerEvents = 'none';
                    effect.style.animation = `explode 1.2s ease-out forwards`;
                    effect.style.setProperty('--angle', (i * 30) + 'deg');
                    effect.style.setProperty('--distance', (Math.random() * 80 + 80) + 'px');
                    effect.style.zIndex = '1000';
                    this.appendChild(effect);
                    
                    setTimeout(() => {
                        effect.remove();
                    }, 1200);
                }
            }, 200);
            
            // 全屏心形雨
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const rainHeart = document.createElement('div');
                    rainHeart.innerHTML = '💕';
                    rainHeart.style.position = 'fixed';
                    rainHeart.style.left = Math.random() * 100 + '%';
                    rainHeart.style.top = '-50px';
                    rainHeart.style.fontSize = (Math.random() * 15 + 15) + 'px';
                    rainHeart.style.pointerEvents = 'none';
                    rainHeart.style.animation = `heartRain 3s ease-in forwards`;
                    rainHeart.style.zIndex = '1000';
                    document.body.appendChild(rainHeart);
                    
                    setTimeout(() => {
                        rainHeart.remove();
                    }, 3000);
                }, i * 100);
            }
        });
        
        // 添加爆炸动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes explode {
                0% {
                    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(0) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(calc(-1 * var(--distance))) scale(0);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // 启动动画
        createFloatingHearts();
        createSparkles();
        
        // 鼠标移动时的星星特效
        document.addEventListener('mousemove', function(e) {
            if (Math.random() < 0.1) {
                const star = document.createElement('div');
                star.innerHTML = '✨';
                star.style.position = 'absolute';
                star.style.left = e.clientX + 'px';
                star.style.top = e.clientY + 'px';
                star.style.fontSize = '16px';
                star.style.pointerEvents = 'none';
                star.style.animation = 'sparkle 1s ease-out forwards';
                star.style.zIndex = '1000';
                document.body.appendChild(star);
                
                setTimeout(() => {
                    star.remove();
                }, 1000);
            }
        });
    </script>
</body>
</html>
