% 生成一个包含10个1-100之间随机数的数列
random_numbers = randi([1, 100], 1, 10);

% 显示生成的随机数列
disp('生成的随机数列:');
disp(random_numbers);

% 调用冒泡排序函数对数列进行排序
sorted_numbers_bubble = bubble_sort(random_numbers);
% 显示排序后的数列
disp('冒泡排序:');
disp(sorted_numbers_bubble);
sorted_numbers_selection = selection_sort(random_numbers);
% 显示选择排序后的数列
disp('选择排序:');
disp(sorted_numbers_selection);


% 冒泡排序函数定义
function sorted_array = bubble_sort(arr)
    n = length(arr);
    for i = 1:n-1
        for j = 1:n-i
            if arr(j) > arr(j+1)
                temp = arr(j);
                arr(j) = arr(j+1);
                arr(j+1) = temp;
            end
        end
    end
    sorted_array = arr;
end
%选择排序
function sorted_array = selection_sort(arr)
    n = length(arr);
    for i = 1:n-1
        min_index = i;
        for j = i+1:n
            if arr(j) < arr(min_index)
                min_index = j;
            end
        end
        if min_index ~= i
            temp = arr(i);
            arr(i) = arr(min_index);
            arr(min_index) = temp;
        end
    end
    sorted_array = arr;
end