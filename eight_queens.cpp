#include <iostream>
#include <vector>
#include <string>

// N 皇后问题的棋盘大小
const int N = 8;

// 用于检查在 (row, col) 放置皇后是否安全
// 使用布尔数组来优化检查
bool is_safe(int row, int col,
             const std::vector<bool>& cols,
             const std::vector<bool>& diag1,
             const std::vector<bool>& diag2) {
    return !cols[col] && !diag1[row - col + N - 1] && !diag2[row + col];
}

// 递归函数，使用 DFS 解决 N 皇后问题
// row: 当前正在考虑的行
// solution_count: 解决方案的数量
// board: 当前解决方案的棋盘
// cols: 记录列是否被占用
// diag1: 记录主对角线 (row - col) 是否被占用
// diag2: 记录副对角线 (row + col) 是否被占用
void solve_n_queens_dfs(int row, int& solution_count, std::vector<std::string>& board,
                        std::vector<bool>& cols,
                        std::vector<bool>& diag1,
                        std::vector<bool>& diag2) {
    // 基本情况：如果所有皇后都已放置，则找到一个解决方案
    if (row == N) {
        solution_count++;
        std::cout << "解决方案 " << solution_count << ":" << std::endl;
        for (int i = 0; i < N; ++i) {
            std::cout << board[i] << std::endl;
        }
        std::cout << std::endl;
        return;
    }

    // 递归情况：尝试在当前行的所有列中放置皇后
    for (int col = 0; col < N; ++col) {
        // 计算对角线索引
        int d1 = row - col + N - 1; // 主对角线 (constant for row - col)
        int d2 = row + col;         // 副对角线 (constant for row + col)

        if (is_safe(row, col, cols, diag1, diag2)) {
            // 放置皇后
            board[row][col] = 'Q';
            cols[col] = true;
            diag1[d1] = true;
            diag2[d2] = true;

            // 递归到下一行
            solve_n_queens_dfs(row + 1, solution_count, board, cols, diag1, diag2);

            // 回溯：移除皇后，尝试其他列
            board[row][col] = '.';
            cols[col] = false;
            diag1[d1] = false;
            diag2[d2] = false;
        }
    }
}

int main() {
    int solution_count = 0;
    std::vector<std::string> board(N, std::string(N, '.')); // 在main函数中初始化棋盘

    // 初始化辅助布尔数组
    std::vector<bool> cols(N, false);
    std::vector<bool> diag1(2 * N - 1, false); // 主对角线数量
    std::vector<bool> diag2(2 * N - 1, false); // 副对角线数量

    std::cout << "正在寻找 " << N << " 皇后问题的解决方案..." << std::endl;
    solve_n_queens_dfs(0, solution_count, board, cols, diag1, diag2); // 传入所有参数
    std::cout << "总共找到 " << solution_count << " 个解决方案。" << std::endl;

    return 0;
}
