#include <iostream>
#include <vector>
#include <string>
#include <queue> // For BFS

// N 皇后问题的棋盘大小
const int N = 8;

// 用于检查在 (row, col) 放置皇后是否安全
// 使用布尔数组来优化检查
bool is_safe(int row, int col,
             const std::vector<bool>& cols,
             const std::vector<bool>& diag1,
             const std::vector<bool>& diag2) {
    return !cols[col] && !diag1[row - col + N - 1] && !diag2[row + col];
}

// 定义 BFS 状态结构
struct State {
    std::vector<std::string> board;
    int row; // 当前正在尝试放置皇后的行
    std::vector<bool> cols;
    std::vector<bool> diag1; // 主对角线 (row - col)
    std::vector<bool> diag2; // 副对角线 (row + col)

    // 初始状态构造函数
    State(int n) : row(0), cols(n, false), diag1(2 * n - 1, false), diag2(2 * n - 1, false) {
        board.assign(n, std::string(n, '.'));
    }

    // 复制构造函数，用于生成新状态
    State(const std::vector<std::string>& b, int r,
          const std::vector<bool>& c,
          const std::vector<bool>& d1,
          const std::vector<bool>& d2)
        : board(b), row(r), cols(c), diag1(d1), diag2(d2) {}
};

// 使用 BFS 解决 N 皇后问题
void solve_n_queens_bfs() {
    int solution_count = 0;
    std::queue<State> q;

    // 将初始状态（空棋盘，从第0行开始）加入队列
    q.push(State(N));

    while (!q.empty()) {
        State current_state = q.front();
        q.pop();

        // 基本情况：如果所有皇后都已放置 (即当前行等于 N)，则找到一个解决方案
        if (current_state.row == N) {
            solution_count++;
            std::cout << "解决方案 " << solution_count << ":" << std::endl;
            for (int i = 0; i < N; ++i) {
                std::cout << current_state.board[i] << std::endl;
            }
            std::cout << std::endl;
            continue; // 继续处理队列中的其他状态
        }

        // 递归情况：尝试在当前行的所有列中放置皇后
        for (int col = 0; col < N; ++col) {
            int d1 = current_state.row - col + N - 1; // 计算主对角线索引
            int d2 = current_state.row + col;         // 计算副对角线索引

            if (is_safe(current_state.row, col, current_state.cols, current_state.diag1, current_state.diag2)) {
                // 创建新状态，复制当前状态
                State next_state = current_state; 

                // 在新状态的棋盘上放置皇后
                next_state.board[next_state.row][col] = 'Q';
                next_state.cols[col] = true;
                next_state.diag1[d1] = true;
                next_state.diag2[d2] = true;
                next_state.row++; // 移动到下一行

                // 将新状态加入队列
                q.push(next_state);
            }
        }
    }
    std::cout << "总共找到 " << solution_count << " 个解决方案。" << std::endl;
}

int main() {
    std::cout << "正在寻找 " << N << " 皇后问题的解决方案 (使用 BFS)..." << std::endl;
    solve_n_queens_bfs();
    return 0;
}